import { ip<PERSON><PERSON><PERSON>, <PERSON><PERSON>erWindow } from 'electron';
import { BOARD_COMMANDS, BOARD_EVENTS } from '../board-constants';

// Types for board state management
interface Card {
  id: string;
  title: string;
  description?: string;
  priority: string;
  projectId?: string;
  dueDate?: string;
  assignee?: string;
  tags: string[];
  subtasks: any[];
  swimlaneId: string;
  columnId: string;
  storyPoints?: number;
  agentAssignments?: any[];
  dependencies?: string[];
  resourceMetrics?: any;
  taskHistory?: any[];
}

interface Column {
  id: string;
  title: string;
  cards: Card[];
}

interface Swimlane {
  id: string;
  title: string;
  isExpanded: boolean;
}

interface BoardState {
  id: string;
  name: string;
  description?: string;
  columns: Column[];
  swimlanes: Swimlane[];
  cardTypes: any[];
  agents: any[];
}

// Global board state storage
const boardStates = new Map<string, BoardState>();

export class BoardStateService {
  private windows: Set<BrowserWindow> = new Set();

  constructor() {
    this.registerIPCHandlers();
  }

  // Register a window to receive board state updates
  registerWindow(window: BrowserWindow) {
    this.windows.add(window);

    // Clean up when window is closed
    window.on('closed', () => {
      this.windows.delete(window);
    });
  }

  // Broadcast state update to all registered windows
  private broadcastStateUpdate(boardId: string, state: BoardState) {
    this.windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(BOARD_EVENTS.STATE_UPDATE, state);
      }
    });
  }

  // Broadcast specific events to all windows
  private broadcastCardCreated(card: Card) {
    this.windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(BOARD_EVENTS.CARD_CREATED, card);
      }
    });
  }

  private broadcastCardUpdated(card: Card) {
    this.windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(BOARD_EVENTS.CARD_UPDATED, card);
      }
    });
  }

  private broadcastCardDeleted(cardId: string, columnId: string) {
    this.windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(BOARD_EVENTS.CARD_DELETED, cardId, columnId);
      }
    });
  }

  private broadcastCardMoved(cardId: string, sourceColumnId: string, targetColumnId: string, swimlaneId: string) {
    this.windows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(BOARD_EVENTS.CARD_MOVED, cardId, sourceColumnId, targetColumnId, swimlaneId);
      }
    });
  }

  // Initialize board state if it doesn't exist
  private initializeBoardIfNeeded(boardId: string): BoardState {
    if (!boardStates.has(boardId)) {
      const defaultBoard: BoardState = {
        id: boardId,
        name: boardId === 'main' ? 'Main Board' : `Board ${boardId}`,
        columns: [
          { id: 'backlog', title: 'Backlog', cards: [] },
          { id: 'todo', title: 'To Do', cards: [] },
          { id: 'in-progress', title: 'In Progress', cards: [] },
          { id: 'review', title: 'Review', cards: [] },
          { id: 'done', title: 'Done', cards: [] },
        ],
        swimlanes: [
          { id: 'default', title: 'Default', isExpanded: true },
        ],
        cardTypes: [],
        agents: [],
      };
      boardStates.set(boardId, defaultBoard);
    }
    return boardStates.get(boardId)!;
  }

  private registerIPCHandlers() {
    // Get board state
    ipcMain.handle(BOARD_COMMANDS.GET_STATE, (_, boardId: string) => {
      return this.initializeBoardIfNeeded(boardId);
    });

    // Create card
    ipcMain.handle(BOARD_COMMANDS.CREATE_CARD, (_, boardId: string, columnId: string, cardData: Omit<Card, 'id'>) => {
      const board = this.initializeBoardIfNeeded(boardId);
      const column = board.columns.find(col => col.id === columnId);

      if (!column) {
        throw new Error(`Column ${columnId} not found`);
      }

      const newCard: Card = {
        ...cardData,
        id: `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        columnId,
      };

      column.cards.push(newCard);

      // Broadcast to all windows
      this.broadcastCardCreated(newCard);
      this.broadcastStateUpdate(boardId, board);

      return newCard;
    });

    // Update card
    ipcMain.handle(BOARD_COMMANDS.UPDATE_CARD, (_, boardId: string, cardId: string, updates: Partial<Card>) => {
      const board = this.initializeBoardIfNeeded(boardId);

      // Find the card across all columns
      let targetCard: Card | null = null;
      for (const column of board.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          targetCard = card;
          Object.assign(card, updates);
          break;
        }
      }

      if (!targetCard) {
        throw new Error(`Card ${cardId} not found`);
      }

      // Broadcast to all windows
      this.broadcastCardUpdated(targetCard);
      this.broadcastStateUpdate(boardId, board);

      return targetCard;
    });

    // Delete card
    ipcMain.handle(BOARD_COMMANDS.DELETE_CARD, (_, boardId: string, columnId: string, cardId: string) => {
      const board = this.initializeBoardIfNeeded(boardId);
      const column = board.columns.find(col => col.id === columnId);

      if (!column) {
        throw new Error(`Column ${columnId} not found`);
      }

      const cardIndex = column.cards.findIndex(card => card.id === cardId);
      if (cardIndex === -1) {
        throw new Error(`Card ${cardId} not found in column ${columnId}`);
      }

      column.cards.splice(cardIndex, 1);

      // Broadcast to all windows
      this.broadcastCardDeleted(cardId, columnId);
      this.broadcastStateUpdate(boardId, board);

      return true;
    });

    // Move card
    ipcMain.handle(BOARD_COMMANDS.MOVE_CARD, (_, boardId: string, cardId: string, sourceColumnId: string, targetColumnId: string, swimlaneId: string, targetIndex?: number) => {
      const board = this.initializeBoardIfNeeded(boardId);
      const sourceColumn = board.columns.find(col => col.id === sourceColumnId);
      const targetColumn = board.columns.find(col => col.id === targetColumnId);

      if (!sourceColumn || !targetColumn) {
        throw new Error('Source or target column not found');
      }

      const cardIndex = sourceColumn.cards.findIndex(card => card.id === cardId);
      if (cardIndex === -1) {
        throw new Error(`Card ${cardId} not found in source column`);
      }

      const [card] = sourceColumn.cards.splice(cardIndex, 1);
      card.columnId = targetColumnId;
      card.swimlaneId = swimlaneId;

      // Insert at specific index or at the end
      if (typeof targetIndex === 'number' && targetIndex >= 0) {
        targetColumn.cards.splice(targetIndex, 0, card);
      } else {
        targetColumn.cards.push(card);
      }

      // Broadcast to all windows
      this.broadcastCardMoved(cardId, sourceColumnId, targetColumnId, swimlaneId);
      this.broadcastStateUpdate(boardId, board);

      return card;
    });

    // Update column
    ipcMain.handle(BOARD_COMMANDS.UPDATE_COLUMN, (_, boardId: string, columnId: string, updates: any) => {
      const board = this.initializeBoardIfNeeded(boardId);
      const column = board.columns.find(col => col.id === columnId);

      if (!column) {
        throw new Error(`Column ${columnId} not found`);
      }

      Object.assign(column, updates);

      // Broadcast to all windows
      this.broadcastStateUpdate(boardId, board);

      return column;
    });

    // Update board
    ipcMain.handle(BOARD_COMMANDS.UPDATE_BOARD, (_, boardId: string, updates: Partial<BoardState>) => {
      const board = this.initializeBoardIfNeeded(boardId);
      Object.assign(board, updates);

      // Broadcast to all windows
      this.broadcastStateUpdate(boardId, board);

      return board;
    });
  }
}
