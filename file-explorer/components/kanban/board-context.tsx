"use client"

import type React from "react"
import { create<PERSON>ontext, useContext, useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { boardIPCBridge } from "./lib/board-agent-api"

// Define shared types for board data structure
export type CardType = {
  id: string
  name: string
  color: string
}

export type AgentAssignment = {
  agentId: string
  agentType: string
  assignmentTime: string
}

export type ResourceMetrics = {
  tokenUsage: number
  cpuTime: number
  memoryUsage: number
}

export type TaskHistoryItem = {
  timestamp: string
  action: string
  agentId: string
  details: string
}

export type Card = {
  id: string
  title: string
  description: string
  priority: string
  dueDate?: string
  labels: CardType[]
  assignee?: string
  attachments?: string[]
  comments?: { id: string; author: string; text: string; timestamp: string }[]
  progress: number
  columnId: string
  swimlaneId?: string
  projectId?: string
  tags?: string[]
  subtasks?: { id: string; title: string; completed: boolean }[]
  agentAssignments: AgentAssignment[]
  dependencies: string[]
  resourceMetrics: ResourceMetrics
  taskHistory: TaskHistoryItem[]
  storyPoints?: number
  updatedAt: string
  createdAt: string
}

export type Column = {
  id: string
  title: string
  cards: Card[]
  limit?: number
  subColumns?: { id: string; title: string }[]
  metadata?: any
}

export type Swimlane = {
  id: string
  title: string
  isExpanded: boolean
}

export type Agent = {
  id: string
  name: string
  type: string
  status: string
  currentTaskId?: string
  capabilities: string[]
  resourceUsage: {
    cpu: number
    memory: number
    tokens: number
  }
}

// Enhanced board type to store complete board data
export type BoardFull = {
  id: string
  name: string
  description?: string
  columns: Column[]
  swimlanes: Swimlane[]
  cardTypes: CardType[]
  agents: Agent[]
}

// Define the context type with comprehensive operations
type BoardContextType = {
  boards: BoardFull[]
  activeBoard: BoardFull | null
  setActiveBoard: (boardId: string) => void
  addBoard: (name: string) => BoardFull
  updateBoard: (id: string, name: string, description?: string) => void
  deleteBoard: (id: string) => void

  // Column operations
  addColumn: (boardId: string, title: string) => string
  updateColumn: (boardId: string, column: Column) => void
  deleteColumn: (boardId: string, columnId: string) => void
  moveColumn: (boardId: string, dragId: string, overId: string | null) => void

  // Swimlane operations
  addSwimlane: (boardId: string, title: string) => string
  updateSwimlane: (boardId: string, swimlane: Swimlane) => void
  deleteSwimlane: (boardId: string, swimlaneId: string) => void
  toggleSwimlaneExpansion: (boardId: string, swimlaneId: string) => void

  // Card operations
  addCardToColumn: (boardId: string, columnId: string, cardData: Omit<Card, "id">) => string
  updateCardInColumn: (boardId: string, updatedCard: Card) => void
  deleteCardFromColumn: (boardId: string, columnId: string, cardId: string) => void
  moveCard: (
    boardId: string,
    cardId: string,
    sourceColumnId: string,
    destinationColumnId: string,
    destinationSwimlaneId: string,
  ) => void

  // Card Type / Legend operations
  updateCardTypes: (boardId: string, cardTypes: CardType[]) => void
  // Agent operations
  updateAgents: (boardId: string, agents: Agent[]) => void
}

// Create the context with default values
const BoardContext = createContext<BoardContextType | undefined>(undefined)

// Default data for new boards
const defaultCardTypes: CardType[] = [
  { id: "low", name: "Low", color: "#22c55e" },
  { id: "medium", name: "Medium", color: "#facc15" },
  { id: "high", name: "High", color: "#ef4444" },
]

const defaultColumns: Column[] = [
  {
    id: "column-1",
    title: "Backlog",
    cards: [],
  },
  {
    id: "column-2",
    title: "Ready",
    cards: [],
  },
  {
    id: "column-3",
    title: "In Development",
    cards: [],
  },
  {
    id: "column-4",
    title: "In Review",
    cards: [],
  },
  {
    id: "column-5",
    title: "Testing / QA",
    cards: [],
  },
  {
    id: "column-6",
    title: "Done",
    cards: [],
  },
]

const defaultSwimlanes: Swimlane[] = [
  {
    id: "swimlane-1",
    title: "Default Swimlane",
    isExpanded: true,
  },
]

const defaultAgents: Agent[] = []

// Sample data for initial boards - REMOVED

// Helper to populate columns with cards
const populateColumnsWithCards = (columns: Column[], cards: Card[]): Column[] => {
  const columnMap: Record<string, Card[]> = {}

  // Group cards by columnId
  cards.forEach((card) => {
    if (!columnMap[card.columnId]) {
      columnMap[card.columnId] = []
    }
    columnMap[card.columnId].push(card)
  })

  // Add cards to their respective columns
  return columns.map((column) => {
    const columnCards = columnMap[column.id] || []
    return { ...column, cards: columnCards }
  })
}

// Initial boards with full structure
const initialFullBoards: BoardFull[] = [
  {
    id: "main",
    name: "Main Development Board",
    columns: populateColumnsWithCards(
      JSON.parse(JSON.stringify(defaultColumns)),
      [] // Empty cards array instead of initialSampleCards
    ),
    swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
    cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
    agents: JSON.parse(JSON.stringify(defaultAgents)),
  },
  {
    id: "frontend",
    name: "Frontend Tasks",
    columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
    swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
    cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
    agents: JSON.parse(JSON.stringify(defaultAgents)),
  },
  {
    id: "backend",
    name: "Backend Tasks",
    columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
    swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
    cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
    agents: JSON.parse(JSON.stringify(defaultAgents)),
  },
]

// Provider component
export function BoardProvider({ children }: { children: React.ReactNode }) {
  const [boards, setBoardsState] = useState<BoardFull[]>([])
  const [activeBoard, setActiveBoardState] = useState<BoardFull | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const { toast } = useToast()
  const { setTheme } = useTheme()

  // Initialize board state from IPC bridge
  useEffect(() => {
    const initializeBoard = async () => {
      try {
        // Try to get state from main process first
        const boardState = await boardIPCBridge.getBoardState('main');
        if (boardState) {
          setBoardsState([boardState]);
          setActiveBoardState(boardState);
        } else {
          // Fallback to default boards if no state exists
          setBoardsState(initialFullBoards);
          setActiveBoardState(initialFullBoards.length > 0 ? initialFullBoards[0] : null);
        }
      } catch (error) {
        console.warn('Failed to initialize board from IPC, using defaults:', error);
        setBoardsState(initialFullBoards);
        setActiveBoardState(initialFullBoards.length > 0 ? initialFullBoards[0] : null);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeBoard();

    // Register listeners for updates from main process
    const unsubscribe = boardIPCBridge.registerEventListeners({
      onStateUpdate: (state) => {
        setBoardsState([state]);
        setActiveBoardState(state);
      },
      onCardCreated: (card) => {
        // Update local state when card is created from another window
        setBoardsState(prevBoards =>
          prevBoards.map(board => {
            if (board.id === card.boardId || board.id === 'main') {
              const updatedColumns = board.columns.map(col => {
                if (col.id === card.columnId) {
                  return { ...col, cards: [...col.cards, card] };
                }
                return col;
              });
              return { ...board, columns: updatedColumns };
            }
            return board;
          })
        );
      },
      onCardUpdated: (card) => {
        // Update local state when card is updated from another window
        setBoardsState(prevBoards =>
          prevBoards.map(board => {
            const updatedColumns = board.columns.map(col => ({
              ...col,
              cards: col.cards.map(c => c.id === card.id ? card : c)
            }));
            return { ...board, columns: updatedColumns };
          })
        );
      },
      onCardDeleted: (cardId, columnId) => {
        // Update local state when card is deleted from another window
        setBoardsState(prevBoards =>
          prevBoards.map(board => {
            const updatedColumns = board.columns.map(col => {
              if (col.id === columnId) {
                return { ...col, cards: col.cards.filter(c => c.id !== cardId) };
              }
              return col;
            });
            return { ...board, columns: updatedColumns };
          })
        );
      },
      onCardMoved: (cardId, sourceColumnId, targetColumnId, swimlaneId) => {
        // Update local state when card is moved from another window
        setBoardsState(prevBoards =>
          prevBoards.map(board => {
            let movedCard: Card | null = null;

            // Remove card from source column
            const updatedColumns = board.columns.map(col => {
              if (col.id === sourceColumnId) {
                const cardIndex = col.cards.findIndex(c => c.id === cardId);
                if (cardIndex !== -1) {
                  movedCard = { ...col.cards[cardIndex], columnId: targetColumnId, swimlaneId };
                  return { ...col, cards: col.cards.filter(c => c.id !== cardId) };
                }
              }
              return col;
            });

            // Add card to target column
            if (movedCard) {
              const finalColumns = updatedColumns.map(col => {
                if (col.id === targetColumnId) {
                  return { ...col, cards: [...col.cards, movedCard!] };
                }
                return col;
              });
              return { ...board, columns: finalColumns };
            }

            return { ...board, columns: updatedColumns };
          })
        );
      },
    });

    return unsubscribe;
  }, []);

  // Initialize theme only once on mount
  useEffect(() => {
    // Check if theme is already set in localStorage
    const savedTheme = localStorage.getItem("theme")
    if (savedTheme) {
      setTheme(savedTheme)

      // Also directly manipulate the DOM for immediate feedback
      if (savedTheme === "dark") {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    } else {
      // Check for system preference
      if (typeof window !== "undefined") {
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
        setTheme(prefersDark ? "dark" : "light")

        // Also directly manipulate the DOM
        if (prefersDark) {
          document.documentElement.classList.add("dark")
        } else {
          document.documentElement.classList.remove("dark")
        }
      }
    }
    // Empty dependency array ensures this only runs once on mount
  }, [])

  // Set active board by ID
  const setActiveBoard = (boardId: string) => {
    const board = boards.find((b) => b.id === boardId)
    if (board) {
      setActiveBoardState(board)
    } else {
      // Fallback if board not found
      if (boards.length > 0) setActiveBoardState(boards[0])
      else setActiveBoardState(null)
    }
  }

  // Add a new board with default structure
  const addBoard = (name: string): BoardFull => {
    const id = name.toLowerCase().replace(/\s+/g, "-") + `-${Date.now()}`
    const newBoard: BoardFull = {
      id,
      name,
      columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
      swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
      cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
      agents: JSON.parse(JSON.stringify(defaultAgents)),
      description: "",
    }
    setBoardsState([...boards, newBoard])
    toast({
      title: "Board created",
      description: `Board "${name}" has been created.`,
    })
    return newBoard
  }

  // Update a board
  const updateBoard = (id: string, name: string, description?: string) => {
    setBoardsState(
      boards.map((board) =>
        board.id === id ? { ...board, name, description: description ?? board.description } : board,
      ),
    )
    if (activeBoard && activeBoard.id === id) {
      setActiveBoardState((prev) => (prev ? { ...prev, name, description: description ?? prev.description } : null))
    }
    toast({
      title: "Board updated",
      description: `Board has been renamed to "${name}".`,
    })
  }

  // Delete a board
  const deleteBoard = (id: string) => {
    if (boards.length <= 1) {
      toast({
        title: "Cannot delete board",
        description: "You must have at least one board.",
        variant: "destructive",
      })
      return
    }
    const updated = boards.filter((board) => board.id !== id)
    setBoardsState(updated)
    if (activeBoard && activeBoard.id === id) {
      setActiveBoard(updated.length > 0 ? updated[0].id : "")
    }
    toast({
      title: "Board deleted",
      description: "The board has been deleted.",
    })
  }

  // Add a new column
  const addColumn = (boardId: string, title: string): string => {
    const newColumnId = `column-${Date.now()}`
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          const newColumn: Column = { id: newColumnId, title, cards: [] }
          return { ...board, columns: [...board.columns, newColumn] }
        }
        return board
      }),
    )

    // Update activeBoard if it's the one being modified
    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        const newColumn: Column = { id: newColumnId, title, cards: [] }
        return { ...prev, columns: [...prev.columns, newColumn] }
      })
    }

    toast({
      title: "Column added",
      description: `Column "${title}" has been added.`,
    })

    return newColumnId
  }

  // Update a column
  const updateColumn = (boardId: string, updatedColumn: Column) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => (col.id === updatedColumn.id ? updatedColumn : col)),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              columns: prev.columns.map((col) => (col.id === updatedColumn.id ? updatedColumn : col)),
            }
          : null,
      )
    }

    toast({
      title: "Column updated",
      description: `Column "${updatedColumn.title}" has been updated.`,
    })
  }

  // Delete a column
  const deleteColumn = (boardId: string, columnId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return { ...board, columns: board.columns.filter((col) => col.id !== columnId) }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              columns: prev.columns.filter((col) => col.id !== columnId),
            }
          : null,
      )
    }

    toast({
      title: "Column deleted",
      description: "The column has been deleted.",
    })
  }

  // Add a new swimlane
  const addSwimlane = (boardId: string, title: string): string => {
    const newSwimlaneId = `swimlane-${Date.now()}`
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true }
          return { ...b, swimlanes: [...b.swimlanes, newSwimlane] }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true }
        return { ...prev, swimlanes: [...prev.swimlanes, newSwimlane] }
      })
    }

    toast({
      title: "Swimlane added",
      description: `Swimlane "${title}" has been added.`,
    })

    return newSwimlaneId
  }

  // Update a swimlane
  const updateSwimlane = (boardId: string, updatedSwimlane: Swimlane) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return { ...b, swimlanes: b.swimlanes.map((s) => (s.id === updatedSwimlane.id ? updatedSwimlane : s)) }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.map((s) => (s.id === updatedSwimlane.id ? updatedSwimlane : s)),
            }
          : null,
      )
    }

    toast({
      title: "Swimlane updated",
      description: `Swimlane "${updatedSwimlane.title}" has been updated.`,
    })
  }

  // Delete a swimlane
  const deleteSwimlane = (boardId: string, swimlaneId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return { ...b, swimlanes: b.swimlanes.filter((s) => s.id !== swimlaneId) }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.filter((s) => s.id !== swimlaneId),
            }
          : null,
      )
    }

    toast({
      title: "Swimlane deleted",
      description: "The swimlane has been deleted.",
    })
  }

  // Toggle swimlane expansion
  const toggleSwimlaneExpansion = (boardId: string, swimlaneId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return {
            ...b,
            swimlanes: b.swimlanes.map((s) => (s.id === swimlaneId ? { ...s, isExpanded: !s.isExpanded } : s)),
          }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.map((s) => (s.id === swimlaneId ? { ...s, isExpanded: !s.isExpanded } : s)),
            }
          : null,
      )
    }
  }

  // Add a card to a column
  const addCardToColumn = (boardId: string, columnId: string, cardData: Omit<Card, "id">): string => {
    // Create local card immediately for responsive UI
    const now = new Date().toISOString()
    const newCardId = `card-${Date.now()}`

    const newCard: Card = {
      ...cardData,
      id: newCardId,
      columnId,
      createdAt: now,
      updatedAt: now,
      taskHistory: [
        ...(cardData.taskHistory || []),
        {
          timestamp: now,
          action: "created",
          agentId: "user",
          details: "Card created",
        },
      ],
    }

    // Update local state immediately
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => {
              if (col.id === columnId) {
                return { ...col, cards: [...col.cards, newCard] }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        return {
          ...prev,
          columns: prev.columns.map((col) => {
            if (col.id === columnId) {
              return { ...col, cards: [...col.cards, newCard] }
            }
            return col
          }),
        }
      })
    }

    // Try to sync with main process in background
    boardIPCBridge.createCard(boardId, columnId, cardData).catch(error => {
      console.warn('Failed to sync card creation with main process:', error);
    });

    toast({
      title: "Card added",
      description: `Card "${newCard.title}" has been added.`,
    })

    return newCardId
  }

  // Update a card in a column
  const updateCardInColumn = (boardId: string, updatedCard: Card) => {

    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => {
              if (col.id === updatedCard.columnId) {
                return {
                  ...col,
                  cards: col.cards.map((c) =>
                    c.id === updatedCard.id ? { ...updatedCard, updatedAt: new Date().toISOString() } : c,
                  ),
                }
              }
              const cardExistsInOtherColumn = col.cards.some(c => c.id === updatedCard.id);
              if (cardExistsInOtherColumn && col.id !== updatedCard.columnId) {
                return {
                  ...col,
                  cards: col.cards.filter(c => c.id !== updatedCard.id)
                }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        return {
          ...prev,
          columns: prev.columns.map((col) => {
            if (col.id === updatedCard.columnId) {
              return {
                ...col,
                cards: col.cards.map((c) =>
                  c.id === updatedCard.id ? { ...updatedCard, updatedAt: new Date().toISOString() } : c,
                ),
              }
            }
            const cardExistsInOtherColumn = col.cards.some(c => c.id === updatedCard.id);
            if (cardExistsInOtherColumn && col.id !== updatedCard.columnId) {
              return {
                ...col,
                cards: col.cards.filter(c => c.id !== updatedCard.id)
              }
            }
            return col
          }),
        }
      })
    }

    toast({
      title: "Card updated",
      description: `Card "${updatedCard.title}" has been updated.`,
    })
  }

  // Delete a card from a column
  const deleteCardFromColumn = (boardId: string, columnId: string, cardId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => {
              if (col.id === columnId) {
                return { ...col, cards: col.cards.filter((c) => c.id !== cardId) }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        return {
          ...prev,
          columns: prev.columns.map((col) => {
            if (col.id === columnId) {
              return { ...col, cards: col.cards.filter((c) => c.id !== cardId) }
            }
            return col
          }),
        }
      })
    }

    toast({
      title: "Card deleted",
      description: "The card has been deleted.",
    })
  }

  // Move a card between columns
  const moveCard = (
    boardId: string,
    cardId: string,
    sourceColumnId: string,
    destinationColumnId: string,
    destinationSwimlaneId: string,
  ) => {
    // Update local state immediately for responsive UI
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          let cardToMove: Card | undefined

          // Remove card from source column
          const columnsWithoutCard = board.columns.map((col) => {
            if (col.id === sourceColumnId) {
              const cardIndex = col.cards.findIndex((c) => c.id === cardId)
              if (cardIndex > -1) {
                cardToMove = col.cards[cardIndex]
                return {
                  ...col,
                  cards: col.cards.filter((c) => c.id !== cardId),
                }
              }
            }
            return col
          })

          if (!cardToMove) return board

          // Update card with new swimlaneId and columnId
          const updatedCard = {
            ...cardToMove,
            swimlaneId: destinationSwimlaneId,
            columnId: destinationColumnId,
            updatedAt: new Date().toISOString(),
            taskHistory: [
              ...cardToMove.taskHistory,
              {
                timestamp: new Date().toISOString(),
                action: "moved",
                agentId: "user",
                details: `Moved from ${sourceColumnId} to ${destinationColumnId}`,
              },
            ],
          }

          // Add card to destination column
          return {
            ...board,
            columns: columnsWithoutCard.map((col) => {
              if (col.id === destinationColumnId) {
                return {
                  ...col,
                  cards: [...col.cards, updatedCard],
                }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    // Update activeBoard if it's the one being modified
    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prevActiveBoard) => {
        if (!prevActiveBoard) return null

        let cardToMove: Card | undefined

        // Remove card from source column
        const columnsWithoutCard = prevActiveBoard.columns.map((col) => {
          if (col.id === sourceColumnId) {
            const cardIndex = col.cards.findIndex((c) => c.id === cardId)
            if (cardIndex > -1) {
              cardToMove = col.cards[cardIndex]
              return {
                ...col,
                cards: col.cards.filter((c) => c.id !== cardId),
              }
            }
          }
          return col
        })

        if (!cardToMove) return prevActiveBoard

        // Update card with new swimlaneId and columnId
        const updatedCard = {
          ...cardToMove,
          swimlaneId: destinationSwimlaneId,
          columnId: destinationColumnId,
          updatedAt: new Date().toISOString(),
          taskHistory: [
            ...cardToMove.taskHistory,
            {
              timestamp: new Date().toISOString(),
              action: "moved",
              agentId: "user",
              details: `Moved from ${sourceColumnId} to ${destinationColumnId}`,
            },
          ],
        }

        // Add card to destination column
        return {
          ...prevActiveBoard,
          columns: columnsWithoutCard.map((col) => {
            if (col.id === destinationColumnId) {
              return {
                ...col,
                cards: [...col.cards, updatedCard],
              }
            }
            return col
          }),
        }
      })
    }

    // Try to sync with main process in background
    boardIPCBridge.moveCard(boardId, cardId, sourceColumnId, destinationColumnId, destinationSwimlaneId).catch(error => {
      console.warn('Failed to sync card move with main process:', error);
    });

    toast({
      title: "Card moved",
      description: "The card has been moved.",
    })
  }

  // Update card types (legend)
  const updateCardTypes = (boardId: string, newCardTypes: CardType[]) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => (board.id === boardId ? { ...board, cardTypes: newCardTypes } : board)),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => (prev ? { ...prev, cardTypes: newCardTypes } : null))
    }

    toast({
      title: "Legend updated",
      description: "The card types have been updated.",
    })
  }

  // Update agents
  const updateAgents = (boardId: string, newAgents: Agent[]) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => (board.id === boardId ? { ...board, agents: newAgents } : board)),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => (prev ? { ...prev, agents: newAgents } : null))
    }

    toast({
      title: "Agents updated",
      description: "The agents have been updated.",
    })
  }

  // Move a column
  const moveColumn = (boardId: string, dragId: string, overId: string | null) => {
    const reorderLogic = (currentColumns: Column[]): Column[] => {
      const oldIndex = currentColumns.findIndex((col) => col.id === dragId);
      if (oldIndex === -1) {
        console.warn(`moveColumn: dragId ${dragId} not found in columns.`);
        return currentColumns; // dragId not found, return original array
      }

      const newColumnsArray = [...currentColumns]; // Clone the array
      const [movedColumn] = newColumnsArray.splice(oldIndex, 1); // Remove the item

      if (overId === null) {
        newColumnsArray.push(movedColumn); // Add to the end if overId is null
      } else {
        const newIndexTarget = currentColumns.findIndex((col) => col.id === overId); // Find target index in original array
        if (newIndexTarget === -1) {
          console.warn(`moveColumn: overId ${overId} not found in columns. Moving to end.`);
          newColumnsArray.push(movedColumn); // Fallback: overId not found, add to end
        } else {
          // Create a temporary array from the original to correctly calculate insertion point
          const tempProcessingArray = [...currentColumns];
          // Remove the item to simulate its state before insertion
          tempProcessingArray.splice(oldIndex, 1);
          // Find the true index of overId in the array *after* dragId has been removed
          const finalTargetIndex = tempProcessingArray.findIndex(col => col.id === overId);

          if (finalTargetIndex !== -1) {
             // If oldIndex was before newIndexTarget, and we are inserting based on newIndexTarget's original position,
             // the actual insertion index in `newColumnsArray` (which is `tempProcessingArray` after removing `movedColumn`)
             // depends on whether `movedColumn` was before or after `overId`.
             // A simpler way is to use the indices on the original array and reconstruct.
             // This is effectively what arrayMove(originalArray, oldIndex, newIndexTarget) does.

             // Re-create the array with the correct order
             const finalOrderedColumns = [...currentColumns]; // Start with the original order
             const itemToMove = finalOrderedColumns.splice(oldIndex, 1)[0]; // Remove from original position
             finalOrderedColumns.splice(newIndexTarget, 0, itemToMove); // Insert at the target's original position
             return finalOrderedColumns;
          } else {
            // This case should ideally not be hit if overId is valid and was found in currentColumns initially.
            // It means overId was the same as dragId, which should be handled by dnd-kit or dragEnd logic.
            // However, as a fallback, place it at the end relative to its current removal.
            newColumnsArray.push(movedColumn);
          }
        }
      }
      return newColumnsArray;
    };

    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return { ...board, columns: reorderLogic(board.columns) };
        }
        return board;
      })
    );

    // Update activeBoard state similarly
    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prevActiveBoard) => {
        if (!prevActiveBoard) return null;
        return { ...prevActiveBoard, columns: reorderLogic(prevActiveBoard.columns) };
      });
    }

    toast({
      title: "Column moved",
      description: "The column order has been updated.",
    });
  };

  return (
    <BoardContext.Provider
      value={{
        boards,
        activeBoard,
        setActiveBoard,
        addBoard,
        updateBoard,
        deleteBoard,
        addColumn,
        updateColumn,
        deleteColumn,
        moveColumn,
        addSwimlane,
        updateSwimlane,
        deleteSwimlane,
        toggleSwimlaneExpansion,
        addCardToColumn,
        updateCardInColumn,
        deleteCardFromColumn,
        moveCard,
        updateCardTypes,
        updateAgents,
      }}
    >
      {children}
    </BoardContext.Provider>
  )
}

// Custom hook to use the board context
export function useBoard() {
  const context = useContext(BoardContext)
  if (context === undefined) {
    throw new Error("useBoard must be used within a BoardProvider")
  }
  return context
}